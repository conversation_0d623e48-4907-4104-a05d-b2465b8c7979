import streamlit as st
import requests
import os

# ---------------------- CONFIGURATION ----------------------
# Get API key from user input
API_KEY = st.text_input(
    "Enter your Firebase Web API Key:",
    type="password",
    placeholder="AIzaSy..."
)

# Get refresh token from Streamlit secrets or environment variable
# For security, store sensitive data in .streamlit/secrets.toml or environment variables
REFRESH_TOKEN = st.secrets.get("FIREBASE_REFRESH_TOKEN") if hasattr(st, 'secrets') and "FIREBASE_REFRESH_TOKEN" in st.secrets else os.getenv("FIREBASE_REFRESH_TOKEN")

# Fallback input if not found in secrets/env
if not REFRESH_TOKEN:
    REFRESH_TOKEN = st.text_input(
        "Enter your Firebase Refresh Token:",
        type="password",
        placeholder="Enter refresh token..."
    )

# ---------------------- FUNCTION TO GET TOKEN ----------------------
def get_firebase_token(api_key: str, refresh_token: str):
    """
    Request a Firebase Secure Token using the refresh token via Google's Secure Token API.
    """
    url = f"https://securetoken.googleapis.com/v1/token?key={api_key}"

    # Manually build the payload as x-www-form-urlencoded string
    payload = f"grant_type=refresh_token&refresh_token={refresh_token}"

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }

    response = requests.post(url, data=payload, headers=headers)

    try:
        return response.json()
    except Exception as e:
        return {"error": f"Failed to parse JSON response: {str(e)}", "raw_response": response.text}

# ---------------------- STREAMLIT DASHBOARD ----------------------
st.set_page_config(page_title="Firebase Token Dashboard", layout="centered")
st.title("🔐 Firebase Secure Token Viewer")

# Information about setting up secrets
with st.expander("ℹ️ How to set up secrets (recommended)"):
    st.markdown("""
    **For better security, store your refresh token in Streamlit secrets:**

    1. Create a file `.streamlit/secrets.toml` in your project directory
    2. Add your refresh token:
    ```toml
    FIREBASE_REFRESH_TOKEN = "your_refresh_token_here"
    ```
    3. The app will automatically use the token from secrets

    **Alternative:** Set the `FIREBASE_REFRESH_TOKEN` environment variable
    """)

if st.button("🎟️ Get Access Token"):
    if not API_KEY:
        st.warning("Please enter your Firebase Web API Key.")
    elif not REFRESH_TOKEN:
        st.warning("Please enter your Firebase Refresh Token.")
    else:
        with st.spinner("Getting access token..."):
            token_response = get_firebase_token(API_KEY, REFRESH_TOKEN)

        st.subheader("🔑 Token Response")

        # Check if there's an error in the response
        if "error" in token_response:
            st.error("❌ Error occurred:")
            st.json(token_response)
        else:
            st.success("✅ Token retrieved successfully!")
            st.json(token_response)
